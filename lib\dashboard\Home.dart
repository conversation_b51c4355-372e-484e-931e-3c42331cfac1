import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'home_component/DashboardCounter.dart';
import '../sip_account/incoming_call_screen.dart';
import '../main.dart';

/*
  Activity name : Home
  Project name : iSalesCRM Mobile App
  Developer : Eng. M A Mazedul Islam
  Designation : Senior Mobile App Developer at iHelpBD Dhaka, Bangladesh.
  Email : <EMAIL>
  Description : This Home screen provides the all dashboard components view
*/

class Home extends StatefulWidget {
  const Home({Key? key}) : super(key: key);

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  TextEditingController callNumber = TextEditingController();
  String callingStatus = "";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: ListView(
      children: [
        //Dashboard counter
        Container(
          height: 650.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
          ),

          // Dashbord home screen
          // New Changed (Nayeem developer.....***********)
          child: const DashboardCounter(),
        ),

        // Test button for incoming call screen
        Container(
          margin: EdgeInsets.all(20.w),
          child: ElevatedButton(
            onPressed: () {
              // Test the incoming call screen navigation
              if (navigatorKey.currentState != null) {
                navigatorKey.currentState!.push(
                  MaterialPageRoute(
                    builder: (context) => const IncomingCallScreen(
                      phoneNumber: "***********",
                      callerName: "Test Caller",
                    ),
                    fullscreenDialog: true,
                  ),
                );
              } else {
                print("Navigator key is null");
              }
            },
            child: const Text("Test Incoming Call Screen"),
          ),
        )
      ],
    ));
  }
}
